﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class InspectionService : IInspectionService
    {
        private readonly IInspectionRepository _IInspectionRepository;

        public InspectionService(IInspectionRepository inspectionRepository)
        {
            _IInspectionRepository = inspectionRepository;
        }

        public long CreateInspection(CreateInspectionDto createInspectionDto)
        {
            var result = _IInspectionRepository.CreateInspection(createInspectionDto);
            return result;
        }

        public int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto)
        {
            var result = _IInspectionRepository.VerifyActionParty(verifyActionPartyDto);
            return result;
        }

        public int VerifyInspector(VerifyInspectorDto verifyInspectorDto)
        {
            var result = _IInspectionRepository.VerifyInspector(verifyInspectorDto);
            return result;
        }

        public List<MstInspection> GetInspections()
        {
            var result = _IInspectionRepository.GetInspections();
            return result;
        }

        public List<MstActionParty> GetActionParties()
        {
            var result = _IInspectionRepository.GetActionParties();
            return result;
        }

        public MstInspection GetInspectionById(int inspectionId)
        {
            var result = _IInspectionRepository.GetInspectionById(inspectionId);
            return result;
        }

        public List<MstInspection> GetInspectionsAssignedToUser(int userId)
        {
            var result = _IInspectionRepository.GetInspectionsAssignedToUser(userId);
            return result;
        }

        public int SendInspectionReminders(InspectionReminderDto reminderDto)
        {
            var result = _IInspectionRepository.SendInspectionReminders(reminderDto);
            return result;
        }

        public int UpdateInspectionItemRemedy(RemedyInspectionDto remedyDto)
        {
            var result = _IInspectionRepository.UpdateInspectionItemRemedy(remedyDto);
            return result;
        }

        public InspectionStatusSummaryDto GetInspectionStatusSummary()
        {
            var result = _IInspectionRepository.GetInspectionStatusSummary();
            return result;
        }

        public InspectionStatusSummaryDto GetInspectionStatusSummaryByFacility(int facilityId)
        {
            var result = _IInspectionRepository.GetInspectionStatusSummaryByFacility(facilityId);
            return result;
        }
    }
}
