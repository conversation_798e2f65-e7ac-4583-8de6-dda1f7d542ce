using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackService _IFeedbackService;
        private readonly ILogger<FeedbackController> _logger;

        public FeedbackController(IFeedbackService feedbackService, IConfiguration configuration, ILogger<FeedbackController> logger)
        {
            _logger = logger;
            _IFeedbackService = feedbackService;
        }

    }
}
