﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface ILoginService
    {
        LoginResponseDto ValidateUser(string? employeeEmail = null, string? employeeId = null, string? fCMToken = null);
    }
}
