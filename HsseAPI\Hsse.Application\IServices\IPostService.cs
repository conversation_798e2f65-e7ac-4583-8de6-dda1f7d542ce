﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface IPostService
    {
        PostsCountsDto GetPostsCountsByUserId(int userId);
        long CreatePost(CreatePostDto createPostDto);
        List<PostDetailsDto> GetPosts(int? userId = null);
        MstPost GetPostById(int postId);
        List<MstPost> GetUnresolvedPosts();
        List<MstPostCategory> GetPostCategories();
        int CreateOrUpdateLikes(CreateLikeDto createLikeDto);
        int ClosePost(ClosedPostDto closedPostDto);
        int CloseObservation(CloseObservationDto closeObservationDto);
        int DeletePost(int postId, int deletedBy);
        int CreateOrUpdateComment(CreateCommentDto createCommentDto);
        int SendDailyReminders();
    }
}
