﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class PostDetailsDto
    {
        public int PostID { get; set; }
        public int UserID { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? PostType { get; set; }
        public string? Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public string? RequiresFollowup { get; set; }
        public int? Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int CommentsCount { get; set; }
        public List<CommentDto> Comments { get; set; } = [];
        public int LikesCount { get; set; }
        public List<LikeDto> Likes { get; set; } = [];
        public List<string> MediaUrls { get; set; } = [];
    }

    public class CommentDto
    {
        public int CommentID { get; set; }
        public int UserID { get; set; }
        public string? CommentText { get; set; }
        public DateTime? CommentedAt { get; set; }
    }

    public class LikeDto
    {
        public int LikeID { get; set; }
        public int UserID { get; set; }
        public bool IsLiked { get; set; }
        public DateTime? LikedAt { get; set; }
    }

}
