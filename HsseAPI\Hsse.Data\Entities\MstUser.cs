﻿using System;
using System.Collections.Generic;

namespace Hsse.Data.Entities;

public partial class MstUser
{
    public int UserId { get; set; }

    public string Username { get; set; } = null!;

    public string FirstName { get; set; } = null!;

    public string LastName { get; set; } = null!;

    public string Email { get; set; } = null!;

    public string? Password { get; set; }

    public string? ProfileImageUrl { get; set; }

    public string? Bio { get; set; }

    public int? PrimaryFacilityId { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? LastLogin { get; set; }

    public bool? IsActive { get; set; }

    public int? SsoUserId { get; set; }

    public bool IsSsoUser { get; set; }

    public string? EmployeeCode { get; set; }

    public virtual ICollection<MstActionPartyUserMapping> MstActionPartyUserMappings { get; set; } = new List<MstActionPartyUserMapping>();

    public virtual ICollection<MstAnnouncementReceiver> MstAnnouncementReceivers { get; set; } = new List<MstAnnouncementReceiver>();

    public virtual ICollection<MstCommentLike> MstCommentLikes { get; set; } = new List<MstCommentLike>();

    public virtual ICollection<MstEventResponse> MstEventResponses { get; set; } = new List<MstEventResponse>();

    public virtual ICollection<MstGroupMember> MstGroupMembers { get; set; } = new List<MstGroupMember>();

    public virtual ICollection<MstGroup> MstGroups { get; set; } = new List<MstGroup>();

    public virtual ICollection<MstLikesConfig> MstLikesConfigs { get; set; } = new List<MstLikesConfig>();

    public virtual ICollection<MstNotification> MstNotifications { get; set; } = new List<MstNotification>();

    public virtual ICollection<MstPostComment> MstPostComments { get; set; } = new List<MstPostComment>();

    public virtual ICollection<MstPost> MstPosts { get; set; } = new List<MstPost>();

    public virtual ICollection<MstUserFacilityMapping> MstUserFacilityMappings { get; set; } = new List<MstUserFacilityMapping>();

    public virtual ICollection<MstUserRolesConfig> MstUserRolesConfigs { get; set; } = new List<MstUserRolesConfig>();
}
