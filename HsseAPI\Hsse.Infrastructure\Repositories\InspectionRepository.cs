﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class InspectionRepository : IInspectionRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public InspectionRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreateInspection(CreateInspectionDto createInspectionDto)
        {
            if (createInspectionDto.InspectionId == 0)
            {
                // Create new inspection
                var newInspection = new MstInspection
                {
                    FacilityId = createInspectionDto.FacilityID,
                    Title = createInspectionDto.Title,
                    Description = createInspectionDto.Description,
                    InspectionDate = createInspectionDto.InspectionDate,
                    CreatedBy = createInspectionDto.CreatedBy,
                    CreatedAt = DateTime.Now,
                    ReferenceNo = createInspectionDto.ReferenceNo
                };

                _MasterDBContext.MstInspections.Add(newInspection);
                _MasterDBContext.SaveChanges();

                // Add inspection items
                foreach (var item in createInspectionDto.InspectionItems)
                {
                    var inspectionItem = new MstInspectionItem
                    {
                        InspectionId = newInspection.InspectionId,
                        Description = item.Description,
                        SpecificLocation = item.SpecificLocation,
                        Recommendation = item.Recommendation,
                        ActionPartyName = item.ActionPartyName,
                        Status = item.Status,
                        Rectification = item.Rectification,
                        AfterImagePath = item.AfterImagePath,
                        CompletionDateTime = item.CompletionDateTime,
                        CreatedAt = DateTime.Now,
                        RecommendationMediaUrl = item.RecommendationMediaUrl,
                        Observation = item.Observation,
                        ObservationMediaUrl = item.ObservationMediaUrl,
                        Verification = item.Verification
                    };

                    _MasterDBContext.MstInspectionItems.Add(inspectionItem);
                }

                _MasterDBContext.SaveChanges();
                return newInspection.InspectionId;
            }
            else
            {
                // Update existing inspection
                var existingInspection = _MasterDBContext.MstInspections
                    .FirstOrDefault(i => i.InspectionId == createInspectionDto.InspectionId);

                if (existingInspection == null)
                    return 0;

                existingInspection.FacilityId = createInspectionDto.FacilityID;
                existingInspection.Title = createInspectionDto.Title;
                existingInspection.Description = createInspectionDto.Description;
                existingInspection.InspectionDate = createInspectionDto.InspectionDate;
                existingInspection.ModifiedAt = DateTime.Now;
                existingInspection.ReferenceNo = createInspectionDto.ReferenceNo;

                // Remove existing items and add new ones
                var existingItems = _MasterDBContext.MstInspectionItems
                    .Where(i => i.InspectionId == createInspectionDto.InspectionId);
                _MasterDBContext.MstInspectionItems.RemoveRange(existingItems);

                // Add new items
                foreach (var item in createInspectionDto.InspectionItems)
                {
                    var inspectionItem = new MstInspectionItem
                    {
                        InspectionId = createInspectionDto.InspectionId,
                        Description = item.Description,
                        SpecificLocation = item.SpecificLocation,
                        Recommendation = item.Recommendation,
                        ActionPartyName = item.ActionPartyName,
                        Status = item.Status,
                        Rectification = item.Rectification,
                        AfterImagePath = item.AfterImagePath,
                        CompletionDateTime = item.CompletionDateTime,
                        CreatedAt = DateTime.Now,
                        ModifiedAt = DateTime.Now,
                        RecommendationMediaUrl = item.RecommendationMediaUrl,
                        Observation = item.Observation,
                        ObservationMediaUrl = item.ObservationMediaUrl,
                        Verification = item.Verification
                    };

                    _MasterDBContext.MstInspectionItems.Add(inspectionItem);
                }

                _MasterDBContext.SaveChanges();
                return existingInspection.InspectionId;
            }
        }

        public int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto)
        {
            var inspectionItem = _MasterDBContext.MstInspectionItems
                .FirstOrDefault(i => i.ItemId == verifyActionPartyDto.ItemId);

            if (inspectionItem == null)
                return 0; // Item not found

            // Store old values for audit
            var oldActionParty = inspectionItem.ActionPartyName?.ToString();
            var oldVerification = inspectionItem.Verification?.ToString();

            inspectionItem.ActionPartyName = verifyActionPartyDto.ActionPartyId;
            inspectionItem.Verification = 1; // Assuming 1 means verified
            inspectionItem.ModifiedAt = DateTime.Now;

            // Add audit log using the correct entity structure
            var auditLog = new MstInspectionItemAudit
            {
                ItemId = verifyActionPartyDto.ItemId,
                OldStatus = oldVerification,
                NewStatus = "1", // Verified
                ChangedBy = verifyActionPartyDto.VerifiedBy,
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstInspectionItemAudits.Add(auditLog);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int VerifyInspector(VerifyInspectorDto verifyInspectorDto)
        {
            var inspection = _MasterDBContext.MstInspections
                .FirstOrDefault(i => i.InspectionId == verifyInspectorDto.InspectionId);

            if (inspection == null)
                return 0; // Inspection not found

            // For inspector verification, we'll use the general audit log table
            // since MstInspectionItemAudit is specifically for inspection items
            var auditLog = new MstAuditLog
            {
                TableName = "MstInspections",
                RecordPrimaryKey = verifyInspectorDto.InspectionId,
                OperationType = "Inspector Verification",
                OldValues = $"Inspector: Not Verified",
                NewValues = $"Inspector: {verifyInspectorDto.InspectorId}, Verified: {verifyInspectorDto.IsVerified}",
                ChangedBy = verifyInspectorDto.InspectorId,
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstAuditLogs.Add(auditLog);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public List<MstInspection> GetInspections()
        {
            return _MasterDBContext.MstInspections.ToList();
        }

        public List<MstActionParty> GetActionParties()
        {
            return _MasterDBContext.MstActionParties.ToList();
        }

        public MstInspection GetInspectionById(int inspectionId)
        {
            return _MasterDBContext.MstInspections
                .FirstOrDefault(i => i.InspectionId == inspectionId);
        }

        public List<MstInspection> GetInspectionsAssignedToUser(int userId)
        {
            // Get inspections where the user is assigned as action party for any inspection item
            var assignedInspectionIds = _MasterDBContext.MstInspectionItems
                .Where(item => item.ActionPartyName == userId)
                .Select(item => item.InspectionId)
                .Distinct()
                .ToList();

            return _MasterDBContext.MstInspections
                .Where(i => assignedInspectionIds.Contains(i.InspectionId))
                .OrderByDescending(i => i.CreatedAt)
                .ToList();
        }

        public int SendInspectionReminders(InspectionReminderDto reminderDto)
        {
            int remindersSent = 0;

            foreach (var actionPartyId in reminderDto.ActionPartyIds)
            {
                // Create notification for each action party
                var notification = new MstNotification
                {
                    UserId = actionPartyId,
                    Heading = "Inspection Reminder",
                    Message = reminderDto.ReminderMessage ?? $"Reminder: You have pending inspection items for Inspection ID: {reminderDto.InspectionID}",
                    IsRead = false,
                    CreatedAt = DateTime.Now
                };

                _MasterDBContext.MstNotifications.Add(notification);
                remindersSent++;
            }

            // Log the reminder activity
            var auditLog = new MstAuditLog
            {
                TableName = "MstInspections",
                RecordPrimaryKey = reminderDto.InspectionID,
                OperationType = "Reminder Sent",
                OldValues = "No Reminder",
                NewValues = $"Reminder sent to {reminderDto.ActionPartyIds.Count} action parties",
                ChangedBy = reminderDto.SentBy,
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstAuditLogs.Add(auditLog);
            _MasterDBContext.SaveChanges();

            return remindersSent;
        }

        public int UpdateInspectionItemRemedy(RemedyInspectionDto remedyDto)
        {
            var inspectionItem = _MasterDBContext.MstInspectionItems
                .FirstOrDefault(item => item.ItemId == remedyDto.ItemID);

            if (inspectionItem == null)
                return 0; // Item not found

            // Update the inspection item with remedy details
            inspectionItem.Rectification = remedyDto.RemedyDescription;
            inspectionItem.Status = remedyDto.IsCompleted ? 2 : 1; // 2 = Completed, 1 = In Progress
            inspectionItem.CompletionDateTime = remedyDto.IsCompleted ? DateTime.Now : null;
            inspectionItem.ModifiedAt = DateTime.Now;

            // Store remedy images (concatenate URLs with separator)
            if (remedyDto.RemedyImageURLs != null && remedyDto.RemedyImageURLs.Any())
            {
                inspectionItem.AfterImagePath = string.Join(";", remedyDto.RemedyImageURLs);
            }

            // Create audit log for the remedy update
            var auditLog = new MstInspectionItemAudit
            {
                ItemId = remedyDto.ItemID,
                NewStatus = remedyDto.IsCompleted ? "Completed" : "In Progress",
                ChangedAt = DateTime.Now
            };

            _MasterDBContext.MstInspectionItemAudits.Add(auditLog);
            _MasterDBContext.SaveChanges();


            return 1; // Success
        }

        public InspectionStatusSummaryDto GetInspectionStatusSummary()
        {
            var summary = new InspectionStatusSummaryDto();

            // Get total inspections
            summary.TotalInspections = _MasterDBContext.MstInspections.Count();

            // Get inspection items statistics
            var allItems = _MasterDBContext.MstInspectionItems.ToList();

            summary.PendingInspections = allItems.Count(item => item.Status == null || item.Status == 1);
            summary.CompletedInspections = allItems.Count(item => item.Status == 2);
            summary.OverdueInspections = allItems.Count(item =>
                (item.Status == null || item.Status == 1) &&
                item.CreatedAt < DateTime.Now.AddDays(-7)); // Consider overdue after 7 days

            // Priority-based statistics (assuming priority is derived from status or other fields)
            summary.HighPriorityItems = allItems.Count(item => item.Status == null); // New items are high priority
            summary.MediumPriorityItems = allItems.Count(item => item.Status == 1); // In progress items
            summary.LowPriorityItems = allItems.Count(item => item.Status == 2); // Completed items

            // Facility-wise summary
            // Materialize inspections and items into memory
            var inspections = _MasterDBContext.MstInspections.ToList();
            var items = _MasterDBContext.MstInspectionItems.ToList();
            var facilities = _MasterDBContext.MstFacilities.ToList();

            var facilitySummary = inspections
               .GroupBy(i => i.FacilityId ?? 0)
               .Select(g =>
               {
                   var inspectionIds = g.Select(i => i.InspectionId).ToHashSet();
                   var facilityItems = items.Where(item => inspectionIds.Contains(item.InspectionId)).ToList();

                   return new InspectionSummaryByFacility
                   {
                       FacilityID = g.Key, // Removed HasValue and Value as g.Key is already an int  
                       FacilityName = facilities.FirstOrDefault(f => f.FacilityId == g.Key)?.FacilityName ?? "Unknown",
                       TotalInspections = g.Count(),
                       PendingItems = facilityItems.Count(item => item.Status == null || item.Status == 1),
                       CompletedItems = facilityItems.Count(item => item.Status == 2)
                   };
               })
               .ToList();


            summary.FacilitySummary = facilitySummary;

            return summary;
        }

        public InspectionStatusSummaryDto GetInspectionStatusSummaryByFacility(int facilityId)
        {
            var summary = new InspectionStatusSummaryDto();

            // Get inspections for specific facility
            var facilityInspections = _MasterDBContext.MstInspections
                .Where(i => i.FacilityId == facilityId)
                .ToList();

            summary.TotalInspections = facilityInspections.Count;

            // Get inspection items for this facility
            var facilityInspectionIds = facilityInspections.Select(i => i.InspectionId).ToList();
            var facilityItems = _MasterDBContext.MstInspectionItems
                .Where(item => facilityInspectionIds.Contains(item.InspectionId))
                .ToList();

            summary.PendingInspections = facilityItems.Count(item => item.Status == null || item.Status == 1);
            summary.CompletedInspections = facilityItems.Count(item => item.Status == 2);
            summary.OverdueInspections = facilityItems.Count(item =>
                (item.Status == null || item.Status == 1) &&
                item.CreatedAt < DateTime.Now.AddDays(-7));

            summary.HighPriorityItems = facilityItems.Count(item => item.Status == null);
            summary.MediumPriorityItems = facilityItems.Count(item => item.Status == 1);
            summary.LowPriorityItems = facilityItems.Count(item => item.Status == 2);

            // Single facility summary
            var facilityName = _MasterDBContext.MstFacilities
                .FirstOrDefault(f => f.FacilityId == facilityId)?.FacilityName ?? "Unknown";

            summary.FacilitySummary = new List<InspectionSummaryByFacility>
            {
                new InspectionSummaryByFacility
                {
                    FacilityID = facilityId,
                    FacilityName = facilityName,
                    TotalInspections = summary.TotalInspections,
                    PendingItems = summary.PendingInspections,
                    CompletedItems = summary.CompletedInspections
                }
            };

            return summary;
        }
    }
}
